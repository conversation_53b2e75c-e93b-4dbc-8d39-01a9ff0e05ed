import { MandinerEnvironment } from './environment.definitions';

// Deves környezet
export const environment: MandinerEnvironment = {
  production: false,
  type: 'dev',
  apiUrl: 'https://kozponti-varnish-publicapi.dev.trendency.hu/publicapi/hu',
  secureApiUrl: 'https://kozponti-varnish-publicapi.dev.trendency.hu/secureapi/hu',
  financialApiUrl: 'https://findata.apptest.content.private/restapi',
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  facebookAppId: '1618891101939213',
  googleClientId: '708972576598-bd1qq5tklrrmvibl303b4f0q0ud1vmu2.apps.googleusercontent.com',
  appleClientId: 'service.hu.mandiner.sso',
  translation: {
    defaultLocale: 'hu',
    locales: ['hu', 'en'],
  },
  siteUrl: 'https://mandiner.dev.trendency.hu',
  withHttps: false,
  googleSiteKey: '6LdOdtgaAAAAADOpTzcEuDkf-oSP16hxYrVwhHR1', // use this key on all site in dev mode
  googleTagManager: 'GTM-TC4WGP7',
  gemiusId: '..iQcfhzv7BVlRP0QFMNQJPZfStuU68PkYZybtIshY7.f7',
  httpReqTimeout: 30, // second
  shopUrls: {
    manageCards: 'https://shop.mandiner.hu/account',
    cancelSubscription: 'https://shop.mandiner.hu/account',
    subscriptions: 'https://www.mandiner-elofizetes.com/elofizetes-start',
  },
  sentry: {
    dsn: 'https://<EMAIL>/50',
    tracingOrigins: ['https://mandiner.dev.trendency.hu/', 'http://localhost:4200', 'localhost'],
    sampleRate: 0.1,
  },
};
