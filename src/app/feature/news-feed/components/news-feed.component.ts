import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>C<PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { StorageService } from '@trendency/kesma-core';
import {
  AnalyticsService,
  Article,
  ArticleBodyDetails,
  ArticleBodyType,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleRouteParams,
  ArticleVideoComponent,
  backendDateToDate,
  BreadcrumbItem,
  buildArticleUrl,
  GalleryData,
  GalleryElementData,
  VoteDataWithAnswer,
  VoteService,
} from '@trendency/kesma-ui';
import { FormatPipeModule } from 'ngx-date-fns';
import { forkJoin, Observable, share, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {
  ArticleCardType,
  ArticleService,
  GalleryService,
  ManAdultComponent,
  ManArticleCardComponent,
  ManBlockTitleSmallComponent,
  ManBreadcrumbComponent,
  MandinerVotingStyle,
  ManGalleryCardComponent,
  ManOpinionCardComponent,
  ManOpinionCardType,
  ManQuizComponent,
  ManSimpleButtonComponent,
  ManSocialShareModalComponent,
  ManSpinnerComponent,
  ManVotingComponent,
  ManWysiwygBoxComponent,
  SocialInteractionEvent,
  SocialInteractionEventType,
} from '../../../shared';
import { ArticleDossierRecommenderComponent } from '../../../shared/components/article-dossier-recommender/article-dossier-recommender.component';
import { NewsFeedService } from '../api/news-feed.service';
import { NewsFeedApiResponseList, NewsFeedDossier } from '../news-feed.definitions';

@Component({
  selector: 'app-news-feed',
  templateUrl: './news-feed.component.html',
  styleUrls: ['./news-feed.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    ManAdultComponent,
    ManBreadcrumbComponent,
    RouterLink,
    FormatPipeModule,
    NgForOf,
    NgTemplateOutlet,
    ManSocialShareModalComponent,
    ManSpinnerComponent,
    ManSimpleButtonComponent,
    ManBlockTitleSmallComponent,
    ManArticleCardComponent,
    NgSwitch,
    NgSwitchCase,
    ManWysiwygBoxComponent,
    ArticleFileLinkDirective,
    ManVotingComponent,
    ArticleDossierRecommenderComponent,
    ManQuizComponent,
    ArticleVideoComponent,
    ManGalleryCardComponent,
    ManOpinionCardComponent,
    AsyncPipe,
  ],
})
export class NewsFeedComponent implements OnInit, OnDestroy {
  articles: Article[] = [];
  breadcrumbItems?: BreadcrumbItem[];
  dossier?: NewsFeedDossier;
  articleSlug?: string;
  isLoading = false;
  galleries: Record<string, GalleryData> = {};
  rowAllCount = 0;
  page = 0;
  isAdultsOnly = false;
  isUserAdultChoice: boolean;

  readonly ArticleCardType = ArticleCardType;
  readonly ArticleBodyType = ArticleBodyType;
  readonly ManOpinionCardType = ManOpinionCardType;
  readonly ManVotingStyle = MandinerVotingStyle;

  private readonly unsubscribe$: Subject<boolean> = new Subject();
  private readonly ARTICLES_PER_PAGE = 3;

  readonly voteCache = this.voteService.voteCache;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly articleService: ArticleService,
    private readonly voteService: VoteService,
    private readonly galleryService: GalleryService,
    private readonly newsFeedService: NewsFeedService,
    private readonly storageService: StorageService,
    private readonly analyticsService: AnalyticsService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.articleSlug = (params as ArticleRouteParams).articleSlug;
    });

    (this.route.data as Observable<NewsFeedApiResponseList>).subscribe(({ data: { result } }) => {
      this.articles = result?.data?.map(this.mapArticleBody.bind(this));
      this.isAdultsOnly = this.articles?.some((article) => article?.isAdultsOnly);
      this.dossier = result?.meta?.newsFeed;
      this.rowAllCount = result?.meta?.limitable?.rowAllCount as number;

      this.isUserAdultChoice = (this.storageService.getSessionStorageData('isAdultChoice', false) ?? false) && this.isAdultsOnly;

      this.setBreadCrumbItems(this.dossier?.title);

      for (const article of this.articles) {
        this.voteService.initArticleVotes(article);
      }

      this.cdr.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  stringToDate(date: string | Date | undefined): Date | null {
    return backendDateToDate(String(date));
  }

  getDossierLink(isNewsFeed = false): string[] {
    const pageType = isNewsFeed ? 'hirfolyam' : 'dosszie';
    return ['/', pageType, this.dossier?.slug as string];
  }

  getArticleLink(article: Article): string[] {
    return buildArticleUrl(article);
  }

  onIsUserAdultChoose(isUserAdult: boolean): void {
    this.isUserAdultChoice = isUserAdult;
  }

  onVotingSubmit($event: string, voteData: VoteDataWithAnswer): void {
    const votingSubmit$ = this.voteService.onVotingSubmit($event, voteData).subscribe({
      complete: () => {
        this.cdr.detectChanges();
        votingSubmit$.unsubscribe();
      },
    });
  }

  onSocialInteraction($event: SocialInteractionEvent): void {
    this.analyticsService.sendSocialInteraction({
      clickLink: $event.url ?? 'no data',
      clickText: $event.linkText ?? 'no data',
    });

    if ($event.event === SocialInteractionEventType.FacebookShare) {
      this.analyticsService.sendFacebookShare({
        clickLink: $event.url ?? 'no data',
        title: $event.title ?? 'no data',
        publishDate: $event.publishDate,
      });
    }
  }

  doubleArticleRecommendations(array: ArticleBodyDetails[]): ArticleBodyDetails[] {
    return array.filter((elem: ArticleBodyDetails) => elem?.value?.id);
  }

  // Any from KESMA-UI ArticleBodyDetails.
  doubleArticleRecommendation(articleBodyDetails: any): ArticleCard {
    if (articleBodyDetails.thumbnailUrl) {
      articleBodyDetails.thumbnail = { url: articleBodyDetails?.thumbnailUrl };
    }
    return {
      ...articleBodyDetails,
      likeCount: articleBodyDetails?.dbcache?.likeCount ?? 0,
      dislikeCount: articleBodyDetails?.dbcache?.dislikeCount ?? 0,
      commentCount: articleBodyDetails?.dbcache?.commentsCount ?? 0,
    };
  }

  loadMoreResults(): void {
    if (!this.canLoadMore || !this.articleSlug) {
      return;
    }

    ++this.page;
    this.isLoading = true;
    const newsFeed$ = this.newsFeedService.getNewsFeed(this.articleSlug, this.page).pipe(share());

    forkJoin({
      newsFeed: newsFeed$,
      // relatedArticles: this.newsFeedService.getRelatedArticles(newsFeed$)
    }).subscribe(({ newsFeed }) => {
      const articles = newsFeed?.data?.map(this.mapArticleBody.bind(this));
      this.articles = this.articles.concat(articles);
      this.isLoading = false;
      this.cdr.detectChanges();
    });
  }

  get canLoadMore(): boolean {
    return this.rowAllCount !== 0 && this.ARTICLES_PER_PAGE * (this.page + 1) < this.rowAllCount;
  }

  private loadEmbeddedGalleries(article: Article): void {
    const bodyElements = article?.body ?? [];

    const gallerySubs = ((bodyElements ?? []) as GalleryElementData[])
      .filter(({ type }) => type === ArticleBodyType.Gallery)
      .map((bodyElem: GalleryElementData) => this.galleryService.getGalleryDetails(bodyElem.details[0].value?.slug));

    forkJoin(gallerySubs)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((galleries) => {
        galleries.forEach((gallery) => {
          this.galleries[gallery.id] = {
            ...gallery,
            highlightedImageUrl: gallery.highlightedImage.url,
          } as GalleryData;
          this.cdr.markForCheck();
        });
      });
  }

  private mapArticleBody(article: Article): Article {
    this.loadEmbeddedGalleries(article);
    return {
      ...article,
      body: this.articleService.prepareArticleBody(article?.body),
      excerpt: article?.lead || article?.excerpt,
    };
  }

  private setBreadCrumbItems(label: string): void {
    this.breadcrumbItems = [{ label: 'Hírfolyam' }, { label }];
  }

  protected readonly votingStyle = MandinerVotingStyle;
}
