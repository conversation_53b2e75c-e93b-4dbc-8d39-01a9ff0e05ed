import { HttpErrorResponse } from '@angular/common/http';
import { Inject, Injectable, Optional } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult, Article, ArticleRouteParams, ArticleSearchResult, RecommendationsData, Tag } from '@trendency/kesma-ui';
import type { Response } from 'express';
import { forkJoin, Observable, of, share, switchMap, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { ApiService, ArticleService, AuthService, MandinerArticleResolverData, SecureApiService } from '../../shared';
import { TagsPageService } from '../tags-page/tags-page.service';

@Injectable({
  providedIn: 'root',
})
export class ArticleResolverService {
  constructor(
    private readonly articleService: ArticleService,
    private readonly tagsService: TagsPageService,
    private readonly router: Router,
    private readonly seoService: SeoService,
    private readonly utilsService: UtilService,
    private readonly apiService: ApiService,
    private readonly secureApi: SecureApiService,
    private readonly authService: AuthService,
    @Inject(RESPONSE) @Optional() private readonly response: Response
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<MandinerArticleResolverData> {
    const params: ArticleRouteParams = route.params as ArticleRouteParams;

    const foundationTagSlug = route.params['slug'];
    const previewHash = params.previewHash;
    let { year, month } = params;
    const categorySlug = params.categorySlug;
    const articleSlug = params.previewHash ? 'cikk-elonezet' : params.articleSlug;
    const previewType = params.previewType ? params.previewType : 'accepted';
    const isYear = !isNaN(parseInt(year as string));
    const isMonth = !isNaN(parseInt(month as string));
    year = isYear && year ? year : undefined;
    month = isYear && month ? month : undefined;
    const URL = foundationTagSlug ? `${foundationTagSlug}` : `${categorySlug}/${year}/${month}/${articleSlug}`;
    let request$: Observable<MandinerArticleResolverData | null>;

    if (previewHash) {
      request$ = forkJoin({
        article: this.articleService.getArticlePreview(articleSlug, previewHash, previewType),
        recommendations: of({} as ApiResult<RecommendationsData>),
        relatedArticles: of({} as ApiResult<ArticleSearchResult[]>),
        articleSlug: of(articleSlug),
        categorySlug: of(categorySlug),
      });
    } else if ((!isYear || !isMonth) && !foundationTagSlug) {
      request$ = forkJoin({
        article: this.redirectOldArticleUrls() as unknown as Observable<ApiResult<Article>>,
        recommendations: of({} as ApiResult<RecommendationsData>),
        relatedArticles: of({} as ApiResult<ArticleSearchResult[]>),
        articleSlug: of(articleSlug),
        categorySlug: of(categorySlug),
      });
    } else {
      request$ = (foundationTagSlug ? this.apiService.getArticlesByFoundationTag(foundationTagSlug) : of({})).pipe(
        switchMap((searchResult: ApiResult<ArticleSearchResult[], ApiResponseMetaList> | any) => {
          let fCategorySlug, fYear, fMonth, fArticleSlug, firstArticleWithFoundationTag;
          if (foundationTagSlug) {
            firstArticleWithFoundationTag = searchResult?.data?.[0];

            if (firstArticleWithFoundationTag) {
              fCategorySlug = firstArticleWithFoundationTag?.columnSlug;
              fYear = firstArticleWithFoundationTag.publishDate.split('-')[0];
              fMonth = firstArticleWithFoundationTag.publishDate.split('-')[1];
              fArticleSlug = firstArticleWithFoundationTag?.slug;
            }
          }

          if (!(foundationTagSlug && firstArticleWithFoundationTag) && !categorySlug) {
            return of(null);
          }

          // share() operator provides only 1 request from BE
          const article$ = (
            foundationTagSlug && firstArticleWithFoundationTag
              ? this.articleService.getArticle(fCategorySlug ?? '', fYear ?? '', fMonth ?? '', fArticleSlug ?? '')
              : this.articleService
                  .getArticle(categorySlug, year as string, month as string, articleSlug)
                  .pipe(tap(({ data }) => this.handleArticleUrlParamRedirects(route, data)))
          ).pipe(share());
          // itemsPerPage must be 9 because response can contain this article too
          const relatedArticles$ = (slugs: string[]): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> =>
            this.tagsService.searchArticleByTags(slugs, 0, 6);
          const freshArticles$ = this.apiService.getFreshArticles([], [], []);

          return forkJoin({
            article: article$.pipe(
              switchMap((article) => {
                return this.articleService.getArticleSocialData(article);
              }),
              switchMap((article) => {
                if (article?.data?.isPaywalled) {
                  return this.getPaidContent(article, articleSlug);
                }

                if (!article?.data?.isOpinion) {
                  return of({ ...article });
                }

                const publicAuthor = article.data?.publicAuthor || 'Mandíner';
                return this.apiService.getOpinionAuthor(publicAuthor).pipe(
                  map((articles) => ({
                    ...article,
                    articles,
                  }))
                );
              })
            ),
            recommendations: of({} as ApiResult<RecommendationsData>),
            relatedArticles: article$.pipe(
              map((res: ApiResult<Article>) => res.data.tags.map((tag: Tag) => tag.slug)),
              switchMap((slugs: string[]) =>
                forkJoin([relatedArticles$(slugs), freshArticles$]).pipe(
                  map(([relatedArticles, freshArticles]) => {
                    const mergedArticles = [...relatedArticles.data, ...freshArticles]
                      .filter((article) => article.slug !== (foundationTagSlug ? searchResult?.data?.[0]?.slug : articleSlug))
                      .slice(0, 8);

                    return {
                      ...relatedArticles,
                      data: mergedArticles,
                    } as unknown as ApiResult<ArticleSearchResult[]>;
                  })
                )
              )
            ),
            articleSlug: of(foundationTagSlug ? searchResult?.data?.[0]?.slug : articleSlug),
            categorySlug: of(foundationTagSlug ? searchResult?.data?.[0]?.categorySlug : categorySlug),
            year: of(foundationTagSlug ? fYear : year),
            month: of(foundationTagSlug ? fMonth : month),
            url: of(URL),
            foundationTagSlug: of(foundationTagSlug),
            foundationTagTitle: of(foundationTagSlug ? searchResult?.data?.[0]?.foundationTagTitle : undefined),
          });
        })
      );
    }

    return (request$ as Observable<MandinerArticleResolverData>).pipe(
      catchError((error: HttpErrorResponse | Error) => {
        console.error('⚠ Error during resolving %s: ', URL, error);

        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });

        return throwError(() => error);
      })
    );
  }

  getPaidContent(article: ApiResult<Article>, articleSlug: string): Observable<ApiResult<Article>> {
    const { columnSlug, year, month } = article.data;
    return this.authService.isAuthenticated().pipe(
      switchMap((isAuthenticated: boolean) => {
        const user = this.authService.currentUser;
        if (!isAuthenticated || !user?.hasValidSubscription) {
          return of(article);
        }

        return this.secureApi.getArticleBody(columnSlug as string, year.toString(), month.toString(), articleSlug).pipe(
          map((result) => {
            article = {
              meta: article.meta,
              data: {
                ...article.data,
                body: result.data?.body,
              },
            };
            return article;
          })
        );
      })
    );
  }

  /**
   * We need to check if the returned article from the API has the same properties that we supplied in the URL.
   * This is necessary because the API returns the article with any year and month parameters that would lead duplicates.
   * @param route
   * @param article
   * @private
   */
  private handleArticleUrlParamRedirects(route: ActivatedRouteSnapshot, article: Article): void {
    const { year, month } = route.params;
    const isYear = !isNaN(parseInt(year as string));
    const isMonth = !isNaN(parseInt(month as string));
    if (!isYear || !isMonth) {
      return;
    }
    const articleYear = article?.publishDate?.getFullYear().toString();
    const articleMonth = article?.publishDate?.getMonth();
    if (!articleYear || articleMonth === undefined) {
      return;
    }
    const articleMonthParam = (articleMonth + 1).toString().padStart(2, '0');
    if (articleYear === year && articleMonthParam === month) {
      return;
    }
    const categorySlug = article.primaryColumn?.slug;
    if (this.utilsService.isBrowser()) {
      this.router.navigate(['/', categorySlug, articleYear, articleMonthParam, article.slug]);
    } else if (this.response) {
      this.response.status(301);
      this.response.setHeader('location', `${environment.siteUrl}/${categorySlug}/${articleYear}/${articleMonthParam}/${article.slug}`);
    }
  }

  // eslint-disable-next-line @typescript-eslint/ban-types
  private redirectOldArticleUrls(): Observable<{}> {
    const currentUrl = this.seoService.currentUrl;
    return this.articleService.getArticleRedirect(encodeURIComponent(currentUrl)).pipe(
      map(({ url }) => {
        if (url && this.utilsService.isBrowser()) {
          window.location.href = url;
        } else if (url && this.response) {
          this.response.status(301);
          // port is missing from the response and `process.env.PORT` reflects
          // the devserver's port when running w/ local devserver -> replacing w/ the predefined
          if (url.match(/^https?:\/\/localhost\//)) {
            url = url.replace(/^https?:\/\/localhost/, environment.siteUrl as string);
          }
          this.response.setHeader('location', url);
        } else {
          this.router.navigate(['/', '404'], {
            skipLocationChange: true,
          });
          return throwError(null);
        }
        return of({});
      })
    );
  }
}
