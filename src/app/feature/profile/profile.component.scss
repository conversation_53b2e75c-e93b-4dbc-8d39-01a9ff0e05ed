@use 'shared' as *;

.profile {
  &-wrapper {
    padding: 30px 0 50px;

    @include media-breakpoint-down(md) {
      padding: 20px 0 50px;
    }
  }

  &-title {
    font-family: var(--kui-font-primary);
    text-transform: uppercase;
    @include media-breakpoint-up(md) {
      text-align: center;
    }
  }

  &-divider {
    width: 100%;
    height: 1px;
    background: var(--kui-gray-400);
    margin: 30px 0;
  }

  &-flex {
    @include media-breakpoint-up(md) {
      display: flex;
      flex-direction: column;
      max-width: 900px;
      margin: auto;
    }
  }

  &-grid-2 {
    @include media-breakpoint-up(md) {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-gap: 24px;
      margin-bottom: 0;
    }
  }

  &-grid-3 {
    @include media-breakpoint-up(md) {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-gap: 24px;
      margin-bottom: 0;
    }
  }

  &-grid-item {
    margin-top: 60px;
  }

  &-data-box {
    border: 2px solid var(--kui-orange-600);
    margin: 0;
    text-align: center;
    min-height: 234px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-header {
    margin: 20px 0 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-title {
      font-family: var(--kui-font-primary);
      color: var(--kui-orange-600);
      font-weight: 700;
      font-size: 24px;
      line-height: 24px;
    }

    &-settings {
      font-weight: 600;
      font-size: 12px;
      text-decoration: underline;
      position: relative;
      cursor: pointer;

      &::before {
        content: '';
        background-size: contain;
        background: url('/src/assets/images/icons/settings.svg') no-repeat;
        width: 12px;
        height: 12px;
        position: absolute;
        top: 2px;
        left: -20px;
      }
    }
  }

  &-top {
    margin-bottom: 20px;
    @include media-breakpoint-up(md) {
      display: block;
      text-align: center;
    }

    &-nickname {
      font-weight: 400;
      font-size: 14px;
    }

    &-divider {
      padding: 0 10px;
    }

    &-email {
      color: var(--kui-orange-600);
      text-decoration: underline;
      font-weight: 400;
      font-size: 14px;
    }
  }

  &-count {
    text-align: center;
    display: flex;
    justify-content: space-between;
    margin: 0 auto 20px;
    gap: 28px;

    &-item {
      &-number {
        font-weight: 700;
        font-size: 32px;
      }

      &-text {
        font-weight: 400;
        font-size: 14px;
      }
    }
  }

  &-title {
    font-weight: 700;
    font-size: 18px;
    line-height: 22px;
    margin-bottom: 20px;
  }

  &-subscription {
    &-box {
      background: var(--kui-black);
      color: var(--kui-white);
      padding: 15px;
      margin-bottom: 20px;
      min-height: 234px;

      &-title {
        color: var(--kui-orange-600);
        font-weight: 800;
        font-size: 16px;
        line-height: 22px;
        text-align: center;
        margin-bottom: 10px;

        &-span {
          font-weight: 400;
          display: block;
        }
      }

      &-list {
        display: inline-block;
        margin-bottom: 10px;

        &-wrapper {
          text-align: center;
        }

        &-item {
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          list-style: disc;
          text-align: left;
        }
      }

      &-deadline {
        text-align: center;
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 10px;
      }

      &-disclaimer {
        text-align: center;
        padding: 0 50px;
      }
    }

    &-box-not-subscribed {
      background: var(--kui-gray-60);
      padding: 15px;
      margin-bottom: 20px;
      min-height: 234px;
      position: relative;

      &-button-wrapper {
        position: absolute;
        bottom: 20px;
        width: calc(100% - 30px);
      }

      &-list {
        display: inline-block;
        margin-bottom: 10px;

        &-wrapper {
          text-align: center;
        }

        &-item {
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
          list-style: disc;
          text-align: left;
        }
      }

      &-deadline {
        text-align: center;
        font-weight: 700;
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 10px;
      }

      &-disclaimer {
        text-align: center;
        padding: 0 50px;
      }
    }
  }

  &-settings-button {
    max-width: 90vw;
    width: 400px;
    --button-bg-light: transparent;
  }
}
