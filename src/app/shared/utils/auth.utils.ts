import { BackendUserLoginRequest, BackendUserRegisterRequest, LoginFormData, RegistrationFormData } from '../definitions';

export function loginFormDataToBackendRequest(formData: LoginFormData, recaptchaToken: string): BackendUserLoginRequest {
  return {
    emailOrUserName: formData.emailOrUsername,
    password: formData.password,
    recaptcha: recaptchaToken,
  };
}

export function registrationFormDataToBackendRequest(formData: RegistrationFormData, recaptchaToken?: string): BackendUserRegisterRequest {
  return {
    lastName: formData.lastName,
    firstName: formData.firstName,
    userName: formData.username,
    email: formData.email,
    plainPassword: formData.password,
    newsletter: formData.newsletter,
    acceptTerms: formData.terms,
    marketingLetter: formData.marketing,
    recaptcha: recaptchaToken,
  };
}
