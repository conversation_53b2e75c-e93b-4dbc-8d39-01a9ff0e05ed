export enum SocialProvider {
  FACEBOOK = 'facebook',
  GOOGLE = 'google',
  APPLE = 'apple',
}

export interface LoginFormData {
  emailOrUsername: string;
  password: string;
}

export interface BackendUserLoginRequest {
  emailOrUserName: string;
  password: string;
  recaptcha: string;
}

export interface BackendUserLoginResponse {
  token: string;
}

export interface BackendSocialLoginResponse extends BackendUserLoginResponse {
  registrationFinished: boolean;
}

export interface BackendAllowedLoginMethodsResponse {
  email: boolean;
  [SocialProvider.FACEBOOK]: boolean;
  [SocialProvider.GOOGLE]: boolean;
  [SocialProvider.APPLE]: boolean;
}

export interface RegistrationFormData {
  lastName: string;
  firstName: string;
  username: string;
  email?: string;
  password?: string;
  newsletter: boolean;
  terms: boolean;
  marketing: boolean;
}

export interface BackendUserRegisterRequest {
  lastName: string;
  firstName: string;
  userName: string;
  email?: string;
  plainPassword?: string;
  acceptTerms: boolean;
  newsletter: boolean;
  marketingLetter: boolean;
  recaptcha?: string;
}

export interface BackendUserResponse {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  userName: string;
  marketingLetter: Date | null;
  newsletter: Date | null;
  acceptTerms: Date | null;
  passwordLastSave: Date | null;
  hasValidSubscription: boolean;
  subscription: BackendSubscription;
  userDetails: BackendUserDetailsResponse;
  isOldOtpSimplePayPayment?: boolean;
}

export interface BackendUserDetailsResponse {
  invoiceName: string;
  invoiceZip: string;
  invoiceCity: string;
  invoiceAddress: string;
  shippingName: string;
  shippingZip: string;
  shippingCity: string;
  shippingAddress: string;
  taxNumber: string;
  phoneNumber: string;
}

export interface BackendSubscription {
  startDate: string;
  endDate: string;
  isGift: boolean;
  createdAt: string;
  isRecurring: boolean;
  product: BackendProduct;
}

export interface BackendProduct {
  name: string;
  price: number;
  periodInDay: number;
  imageUrl?: string;
  campaign: BackendCampaign;
}

export interface BackendCampaign {
  name: string;
  description: string;
  url?: string;
  type: CampaignTypeEnum;
}

export interface VerifyRegisterResponse {
  status: string;
  needToSetPassword: boolean;
  savePasswordUrl: string;
  userData: {
    userName: string;
  };
}

export declare enum CampaignTypeEnum {
  NORMAL = 'normal',
  STUDENT = 'student',
  PENSIONER = 'pensioner',
}
