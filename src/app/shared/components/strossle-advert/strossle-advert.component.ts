import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgIf, AsyncPipe } from '@angular/common';
import { AdvertService } from '../../services/advert.service';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-strossle-advert',
  templateUrl: './strossle-advert.component.html',
  styleUrls: ['./strossle-advert.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AsyncPipe],
})
export class StrossleAdvertComponent implements OnInit {
  @Input() advertId: string;
  hasDebug: boolean;
  isAdEnabled$: Observable<boolean>;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly advertService: AdvertService
  ) {}

  ngOnInit(): void {
    this.hasDebug = this.route.snapshot.queryParamMap.has('apptest');
    this.isAdEnabled$ = this.advertService.enableAdsSubject.asObservable();
  }
}
