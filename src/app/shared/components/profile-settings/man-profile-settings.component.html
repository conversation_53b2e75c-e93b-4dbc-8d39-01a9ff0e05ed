<div class="profile-settings-container" *ngIf="user">
  <!-- User login settings -->
  <section class="profile-settings-box">
    <div class="profile-settings-login-data">
      <h2 class="profile-settings-box-title">Belépési adatok</h2>
      <div class="profile-settings-box-title-line"></div>
      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Felhasználónév</h3>
        <div class="profile-settings-box-text">
          <span class="text-bold text-yellow">{{ user?.username ?? 'Nincs becenév beállítva' }}</span>
        </div>
      </div>

      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">E-mail-cím</h3>
        <div class="profile-settings-box-text">
          <span class="profile-settings-box-link">{{ user?.email }}</span>
        </div>
      </div>

      <div class="profile-settings-row" *ngIf="user?.passwordLastSave">
        <h3 class="profile-settings-box-label">Jels<PERSON><PERSON></h3>
        <div class="profile-settings-box-text">Uto<PERSON>ó módosítás: {{ (user?.passwordLastSave | date: DATETIME_FORMAT) ?? 'N/A' }}</div>
      </div>
    </div>
    <man-simple-button routerLink="/profil/beallitasok/belepesi-adatok">Belépési adatok módosítása » </man-simple-button>
  </section>

  <div class="profile-settings-box-divider"></div>

  <!-- Subscription settings -->
  <section class="profile-settings-box">
    <div class="profile-settings-subscription-settings">
      <h2 class="profile-settings-box-title">Mandiner-előfizetési beállítások</h2>
      <div class="profile-settings-box-title-line"></div>
      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Előfizetés típusa</h3>
        <div class="profile-settings-box-text">
          <ng-container *ngIf="user?.subscription; else noSubscription">
            {{ user?.subscription?.product?.name }}
          </ng-container>
        </div>
      </div>

      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Előfizetési időszak</h3>
        <div class="profile-settings-box-desc">
          <div class="profile-settings-box-text">
            <ng-container *ngIf="user?.subscription; else noSubscription">
              {{ user?.subscription?.startDate ?? 'N/A' }} -
              {{ user?.subscription?.endDate ?? 'N/A' }}
            </ng-container>
          </div>
          <div class="profile-settings-box-text" *ngIf="user?.subscription && !user?.subscription?.product?.isDigital">
            <span class="text-gray">A nyomtatott előfizetés {{ user?.subscription?.startDate }} napjától indul.</span>
          </div>
          <div class="profile-settings-box-text" *ngIf="user?.subscription?.isRecurring">
            <span class="text-gray">Az előfizetés az időszak végén automatikusan megújul.</span>
          </div>
          <div class="profile-settings-box-text" *ngIf="user?.subscription && !user?.subscription?.isRecurring">
            <span class="text-gray">Az előfizetés az időszak végén megszűnik.<br />Új csomagra a lejárat utáni napon tud majd előfizetni.</span>
          </div>
        </div>
      </div>

      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Fizetési mód</h3>
        <div class="profile-settings-box-desc">
          <div class="profile-settings-box-text">
            <ng-container *ngIf="user?.subscription; else noSubscription"> Online bankkártyás fizetés </ng-container>
          </div>
          <div class="profile-settings-box-text" *ngIf="user?.subscription?.isRecurring">
            <span class="text-gray">A mindenkor esedékes előfizetési díjat az előfizetési időszak végén automatikusan levonjuk a kártyáról.</span>
          </div>
        </div>
      </div>
    </div>
    <ng-template #noSubscription>
      <span class="text-gray">Nincs előfizetés</span>
    </ng-template>
    <man-simple-button class="action-button" *ngIf="user?.subscription?.isRecurring" [disabled]="!user?.subscription?.isRecurring" (click)="handleCardChange()">
      Kártya módosítása »
    </man-simple-button>
    <man-simple-button class="action-button" *ngIf="user?.subscription" [disabled]="!user?.subscription?.isRecurring" (click)="handleCancelSubscription()">{{
      user?.subscription?.isRecurring ? 'Előfizetés lemondása »' : 'Az előfizetés lemondva'
    }}</man-simple-button>
    <man-simple-button *ngIf="!user?.subscription" (click)="handleSubscription()">Tovább az előfizetésekhez »</man-simple-button>
  </section>

  <!-- Advertisement subscription settings -->
  <section *ngIf="user?.hasValidSubscriptionAdvertise && user?.subscriptionAdvertise?.orderId !== user?.subscription?.orderId" class="profile-settings-box">
    <div class="profile-settings-subscription-settings">
      <div class="profile-settings-box-title-line"></div>
      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Előfizetés típusa</h3>
        <div class="profile-settings-box-text">
          <ng-container *ngIf="user?.subscriptionAdvertise; else noSubscription"> {{ user?.subscriptionAdvertise?.name }} </ng-container>
        </div>
      </div>

      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Előfizetési időszak</h3>
        <div class="profile-settings-box-desc">
          <div class="profile-settings-box-text">
            <ng-container *ngIf="user?.subscriptionAdvertise; else noSubscription">
              {{ user?.subscriptionAdvertise?.dateFrom ?? 'N/A' }} -
              {{ user?.subscriptionAdvertise?.dateTo ?? 'N/A' }}
            </ng-container>
          </div>
          <div class="profile-settings-box-text" *ngIf="user?.subscriptionAdvertise?.isRecurring">
            <span class="text-gray">Az előfizetés az időszak végén automatikusan megújul.</span>
          </div>
          <div class="profile-settings-box-text" *ngIf="user?.subscriptionAdvertise && !user?.subscriptionAdvertise?.isRecurring">
            <span class="text-gray">Az előfizetés az időszak végén megszűnik.<br />Új csomagra a lejárat utáni napon tud majd előfizetni.</span>
          </div>
        </div>
      </div>

      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Fizetési mód</h3>
        <div class="profile-settings-box-desc">
          <div class="profile-settings-box-text">
            <ng-container *ngIf="user?.subscriptionAdvertise; else noSubscription"> Online bankkártyás fizetés </ng-container>
          </div>
          <div class="profile-settings-box-text" *ngIf="user?.subscriptionAdvertise?.isRecurring">
            <span class="text-gray">A mindenkor esedékes előfizetési díjat az előfizetési időszak végén automatikusan levonjuk a kártyáról.</span>
          </div>
        </div>
      </div>
    </div>
    <ng-template #noSubscription>
      <span class="text-gray">Nincs előfizetés</span>
    </ng-template>
    <man-simple-button
      class="action-button"
      *ngIf="user?.subscriptionAdvertise?.isRecurring"
      [disabled]="!user?.subscriptionAdvertise?.isRecurring"
      (click)="handleCardChange()"
    >
      Kártya módosítása »
    </man-simple-button>
    <man-simple-button
      class="action-button"
      *ngIf="user?.subscriptionAdvertise"
      [disabled]="!user?.subscriptionAdvertise?.isRecurring"
      (click)="handleCancelSubscription()"
      >{{ user?.subscriptionAdvertise?.isRecurring ? 'Előfizetés lemondása »' : 'Az előfizetés lemondva' }}</man-simple-button
    >
    <man-simple-button *ngIf="!user?.subscriptionAdvertise" (click)="handleSubscription()">Tovább az előfizetésekhez »</man-simple-button>
  </section>

  <div class="profile-settings-box-divider"></div>

  <!-- Billing and shipping settings -->
  <section class="profile-settings-box">
    <div class="profile-settings-subscriber-and-billing-data">
      <h2 class="profile-settings-box-title">Előfizetői és számlázási adatok</h2>
      <div class="profile-settings-box-title-line"></div>
      <div class="profile-settings-row" *ngIf="user?.lastName && user?.firstName">
        <h3 class="profile-settings-box-label">Előfizető neve</h3>
        <div class="profile-settings-box-text">{{ user?.lastName + ' ' + user?.firstName }}</div>
      </div>

      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Postázási cím</h3>
        <div class="profile-settings-box-text">
          <span class="text-gray" *ngIf="!user?.userDetails?.shippingAddress; else shippingAddress">Nincs beállítva postázási cím</span>
          <ng-template #shippingAddress>
            <div>{{ user?.userDetails?.shippingName }}</div>
            <div>{{ user?.userDetails?.shippingZip }} {{ user?.userDetails?.shippingCity }}</div>
            <div>{{ user?.userDetails?.shippingAddress }}</div>
          </ng-template>
        </div>
      </div>

      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Számlázási adatok</h3>
        <div class="profile-settings-box-text">
          <span class="text-gray" *ngIf="!user?.userDetails?.invoiceAddress; else billingAddress">Nincs beállítva számlázási cím</span>
          <ng-template #billingAddress>
            <div>
              {{ user?.userDetails?.invoiceName }}
              <ng-container *ngIf="user?.userDetails?.taxNumber">({{ user?.userDetails?.taxNumber }})</ng-container>
            </div>
            <div>{{ user?.userDetails?.invoiceZip }} {{ user?.userDetails?.invoiceCity }}</div>
            <div>{{ user?.userDetails?.invoiceAddress }}</div>
          </ng-template>
        </div>
      </div>

      <div class="profile-settings-warning">
        *Amennyiben előfizetési és számlázási adatain kíván módosítani, kérjük, jelezze e-mailben a
        <a href="mailto:<EMAIL>">kapcsolat&#64;mandiner.hu</a> címen.
      </div>
    </div>
  </section>

  <div class="profile-settings-box-divider"></div>

  <!-- Terms and newsletter settings -->
  <section class="profile-settings-box">
    <div class="profile-settings-statements">
      <h2 class="profile-settings-box-title">Nyilatkozatok</h2>
      <div class="profile-settings-box-title-line"></div>

      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Felhasználási feltételek és adatkezelési nyilatkozat</h3>
        <div class="profile-settings-box-text">Elfogadás időpontja: {{ (user?.acceptTerms | date: DATETIME_FORMAT) ?? 'N/A' }}</div>
      </div>

      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Piackutatás és személyre szabott ajánlatok</h3>
        <div class="profile-settings-box-text">
          <span class="text-gray" *ngIf="!user?.marketingLetter; else marketingLetterDate">Nem fogadta el a nyilatkozatot.</span>
          <ng-template #marketingLetterDate>Elfogadás időpontja: {{ (user?.marketingLetter | date: DATETIME_FORMAT) ?? 'N/A' }}</ng-template>
        </div>
      </div>

      <div class="profile-settings-row">
        <h3 class="profile-settings-box-label">Rendszeres hírlevél nyilatkozat</h3>
        <div class="profile-settings-box-text">
          <span class="text-gray" *ngIf="!user?.newsletter; else newsletterDate">Nem fogadta el a nyilatkozatot.</span>
          <ng-template #newsletterDate>Elfogadás időpontja: {{ (user?.newsletter | date: DATETIME_FORMAT) ?? 'N/A' }}</ng-template>
        </div>
      </div>
    </div>
    <man-simple-button routerLink="/profil/beallitasok/nyilatkozatok">Nyilatkozatok módosítása »</man-simple-button>
  </section>

  <button class="profile-settings-btn-transparent" type="button" routerLink="/profil/beallitasok/torles">
    <i class="icon-dustbin"></i>
    Mandiner-profil és személyes adatok törlése
  </button>
</div>
