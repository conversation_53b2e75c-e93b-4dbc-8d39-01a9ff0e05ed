import { DatePipe, NgIf } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { RouterLink } from '@angular/router';
import { User } from '@trendency/kesma-ui';
import { environment } from '../../../../environments/environment';
import { DATETIME_FORMAT } from '../../constants';
import { BackendSubscriptionAdvertise } from '../../definitions';
import { ManSimpleButtonComponent } from '../simple-button/man-simple-button.component';

@Component({
  selector: 'man-profile-settings',
  templateUrl: './man-profile-settings.component.html',
  styleUrls: ['./man-profile-settings.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ManSimpleButtonComponent, RouterLink, DatePipe],
})
export class ManProfileSettingsComponent {
  readonly DATETIME_FORMAT = DATETIME_FORMAT;

  @Input() user?: User & { isOldOtpSimplePayPayment?: boolean; hasValidSubscriptionAdvertise?: boolean; subscriptionAdvertise?: BackendSubscriptionAdvertise };
  @Output() legacyCancelSubscription = new EventEmitter<void>();

  handleCancelSubscription(): void {
    if (this.user?.isOldOtpSimplePayPayment) {
      // Legacy cancel subscription
      this.legacyCancelSubscription.emit();
      return;
    }
    window.open(environment.shopUrls.cancelSubscription);
  }

  handleCardChange(): void {
    window.open(environment.shopUrls.manageCards);
  }

  handleSubscription(): void {
    window.open(environment.shopUrls.subscriptions);
  }
}
