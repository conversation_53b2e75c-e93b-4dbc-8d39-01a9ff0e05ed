import { DOCUMENT } from '@angular/common';
import { inject, Inject, Injectable, Injector, runInInjectionContext } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { ScriptLoaderService, User } from '@trendency/kesma-ui';
import { BehaviorSubject, interval, Subject, takeUntil, timer } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AdvertService {
  constructor(
    private readonly injector: Injector,
    private readonly utilsService: UtilService,
    private readonly scriptLoaderService: ScriptLoaderService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  destroyInterval$ = new Subject<void>();
  readonly enableAdsSubject = new BehaviorSubject<boolean>(true);
  private advertScriptLoaded = false;
  private userAuthenticationChecked = false;

  disableAds(): void {
    this.enableAdsSubject.next(false);
    this.addAdsDoNotShowClass();
    this.advertInterval();
    this.removeDynamicAdElements();
  }

  enableAds(): void {
    this.enableAdsSubject.next(true);
    this.destroyInterval$.next();
    this.removeAdsDoNotShowClass();
    this.advertInterval();
  }

  isAdEnabled(): boolean {
    return this.enableAdsSubject.getValue();
  }

  callAdvertScriptOnNavigation(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    setTimeout(() => {
      if (this.isAdEnabled()) {
        this.reloadAds();
      } else {
        this.clearAds();
      }
    }, 1000);
  }

  advertInterval(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (this.isAdEnabled()) {
      return;
    }

    const advertCleaner$ = interval(500).pipe(takeUntil(timer(5000)), takeUntil(this.destroyInterval$));
    advertCleaner$.subscribe(() => {
      if (!this.isAdEnabled()) {
        this.clearAds();
      }
    });
  }

  clearAds(): void {
    if (!this.utilsService.isBrowser() || window?.__adsConfig === undefined) return;

    window.__adsConfig?.clearAds();
    this.removeDynamicAdElements();
  }

  reloadAds(): void {
    if (!this.utilsService.isBrowser() || window?.__adsConfig === undefined) return;

    window.__adsConfig?.spaHardReset();
    window.__adsConfig?.reinsertStrossle();
  }

  handleAdDisabling(isWithoutAds: boolean): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (!isWithoutAds === this.enableAdsSubject.getValue()) {
      return;
    }

    if (isWithoutAds) {
      this.disableAds();
    } else {
      this.enableAds();
    }
  }

  loadAdvertScript(): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    if (this.advertScriptLoaded) {
      return;
    }

    setTimeout(() => {
      runInInjectionContext(this.injector, () => {
        const activatedRoute = inject(ActivatedRoute, { optional: true });
        if (activatedRoute?.snapshot?.firstChild?.data?.['skipSsrConditionalElements']) {
          return;
        }
        const todayDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');

        const advertSrc = 'https://cdn-alpha.adsinteractive.com/mandiner.hu.js?v=' + todayDate;

        this.scriptLoaderService.loadScript(advertSrc, true, false);
        this.advertScriptLoaded = true;
      });
    }, 500);
  }

  addAdsDoNotShowClass(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    this.document.body.classList.add('adsDoNotShowAds');
  }

  removeAdsDoNotShowClass(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    this.document.body.classList.remove('adsDoNotShowAds');
  }

  /**
   * Removes advert script from the DOM
   */
  removeAdvertScript(): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    const advertScripts = this.document.querySelectorAll('script[src*="adsinteractive.com"]');
    advertScripts.forEach((script) => script.remove());

    if (window?.__adsConfig) {
      window.__adsConfig = undefined;
    }

    this.advertScriptLoaded = false;
    this.removeDynamicAdElements();
  }

  /**
   * Removes dynamically created ad elements that are generated by ad scripts
   */
  private removeDynamicAdElements(): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    // Remove footer banner ads
    const footerBanners = this.document.querySelectorAll('.ads-footer-banner');
    footerBanners.forEach((element) => element.remove());

    // Remove Google Ads iframes and containers
    const googleAdsIframes = this.document.querySelectorAll('iframe[id*="google_ads_iframe"]');
    googleAdsIframes.forEach((iframe) => {
      const container = iframe.closest('[id*="google_ads_iframe"][id*="container"]') || iframe.parentElement;
      if (container) {
        container.remove();
      } else {
        iframe.remove();
      }
    });

    // Remove any elements with Google Ads container IDs
    const googleAdsContainers = this.document.querySelectorAll('[id*="google_ads_iframe"][id*="container"]');
    googleAdsContainers.forEach((container) => container.remove());

    // Remove any divs that contain ad-related classes or IDs
    const adElements = this.document.querySelectorAll('[class*="ads_"], [id*="Mandiner_"]:not(app-strossle-advert)');
    adElements.forEach((element) => {
      // Only remove if it's not our Angular component
      if (!element.hasAttribute('_nghost-') && !element.hasAttribute('_ngcontent-')) {
        element.remove();
      }
    });

    // Remove any remaining ad-related elements
    const adRelatedElements = this.document.querySelectorAll('[data-google-query-id], [data-google-container-id]');
    adRelatedElements.forEach((element) => element.remove());
  }

  /**
   * Loads advert script conditionally based on user subscription status
   */
  loadAdvertScriptConditionally(user: User | undefined, hasToken: boolean): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    if (!hasToken) {
      this.userAuthenticationChecked = true;
      this.loadAdvertScript();
      return;
    }

    if (user) {
      this.userAuthenticationChecked = true;
      const hasValidSubscriptionAdvertise = (user as any).hasValidSubscriptionAdvertise;

      if (hasValidSubscriptionAdvertise) {
        this.disableAds();
        this.removeAdvertScript();
      } else {
        this.enableAds();
        if (!this.advertScriptLoaded) {
          this.loadAdvertScript();
        }
      }
    }
  }

  /**
   * Handles user login after initial site visit
   */
  handleUserLoginAfterVisit(user: User | undefined): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (!user) {
      return;
    }

    const hasValidSubscriptionAdvertise = (user as any).hasValidSubscriptionAdvertise;

    if (hasValidSubscriptionAdvertise) {
      this.disableAds();
      this.removeAdvertScript();
      this.startPeriodicAdCleanup();
    } else {
      this.enableAds();
      if (!this.advertScriptLoaded) {
        this.loadAdvertScript();
      }
    }
  }

  /**
   * Starts periodic cleanup of dynamic ad elements for subscribed users
   */
  private startPeriodicAdCleanup(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    const two_seconds = 2000;
    const ten_seconds = 10000;
    const cleanup$ = interval(two_seconds).pipe(takeUntil(timer(ten_seconds)), takeUntil(this.destroyInterval$));
    cleanup$.subscribe(() => {
      if (!this.isAdEnabled()) {
        this.removeDynamicAdElements();
      }
    });
  }

  /**
   * Checks if user authentication has been processed
   */
  isUserAuthenticationChecked(): boolean {
    return this.userAuthenticationChecked;
  }
}
