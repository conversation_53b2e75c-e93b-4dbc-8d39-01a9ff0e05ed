import { DOCUMENT } from '@angular/common';
import { inject, Inject, Injectable, Injector, runInInjectionContext } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { ScriptLoaderService, User } from '@trendency/kesma-ui';
import { BehaviorSubject, interval, Subject, takeUntil, timer } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AdvertService {
  constructor(
    private readonly injector: Injector,
    private readonly utilsService: UtilService,
    private readonly scriptLoaderService: ScriptLoaderService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  destroyInterval$ = new Subject<void>();
  readonly enableAdsSubject = new BehaviorSubject<boolean>(true);
  private advertScriptLoaded = false;
  private userAuthenticationChecked = false;

  disableAds(): void {
    this.enableAdsSubject.next(false);
    this.addAdsDoNotShowClass();
    this.advertInterval();
    // Remove any existing Google Ads elements
    this.removeGoogleAdElements();
  }

  enableAds(): void {
    this.enableAdsSubject.next(true);
    this.destroyInterval$.next();
    this.removeAdsDoNotShowClass();
    this.advertInterval();
  }

  isAdEnabled(): boolean {
    return this.enableAdsSubject.getValue();
  }

  callAdvertScriptOnNavigation(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    setTimeout(() => {
      if (this.isAdEnabled()) {
        this.reloadAds();
      } else {
        this.clearAds();
        // Remove Google Ads elements on navigation for subscribed users
        this.removeGoogleAdElements();
      }
    }, 1000);
  }

  advertInterval(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (this.isAdEnabled()) {
      return;
    }

    const advertCleaner$ = interval(500).pipe(takeUntil(timer(5000)), takeUntil(this.destroyInterval$));
    advertCleaner$.subscribe(() => {
      if (!this.isAdEnabled()) {
        this.clearAds();
      }
    });
  }

  clearAds(): void {
    if (!this.utilsService.isBrowser() || window?.__adsConfig === undefined) return;

    window.__adsConfig?.clearAds();
  }

  reloadAds(): void {
    if (!this.utilsService.isBrowser() || window?.__adsConfig === undefined) return;

    window.__adsConfig?.spaHardReset();
    window.__adsConfig?.reinsertStrossle();
  }

  handleAdDisabling(isWithoutAds: boolean): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (!isWithoutAds === this.enableAdsSubject.getValue()) {
      return;
    }

    if (isWithoutAds) {
      this.disableAds();
    } else {
      this.enableAds();
    }
  }

  loadAdvertScript(): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    if (this.advertScriptLoaded) {
      return;
    }

    setTimeout(() => {
      runInInjectionContext(this.injector, () => {
        const activatedRoute = inject(ActivatedRoute, { optional: true });
        if (activatedRoute?.snapshot?.firstChild?.data?.['skipSsrConditionalElements']) {
          return;
        }
        const todayDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');

        const advertSrc = 'https://cdn-alpha.adsinteractive.com/mandiner.hu.js?v=' + todayDate;

        this.scriptLoaderService.loadScript(advertSrc, true, false);
        this.advertScriptLoaded = true;
      });
    }, 500);
  }

  addAdsDoNotShowClass(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    this.document.body.classList.add('adsDoNotShowAds');
  }

  removeAdsDoNotShowClass(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    this.document.body.classList.remove('adsDoNotShowAds');
  }

  /**
   * Removes advert script from the DOM
   */
  removeAdvertScript(): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    const advertScripts = this.document.querySelectorAll('script[src*="adsinteractive.com"]');
    advertScripts.forEach((script) => script.remove());

    if (window?.__adsConfig) {
      window.__adsConfig = undefined;
    }

    // Remove Google Ads elements with data-google-query-id
    this.removeGoogleAdElements();

    this.advertScriptLoaded = false;
  }

  /**
   * Removes Google Ads elements with data-google-query-id and related attributes
   */
  private removeGoogleAdElements(): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    // Remove elements with data-google-query-id (like your example)
    const googleQueryElements = this.document.querySelectorAll('[data-google-query-id]');
    googleQueryElements.forEach((element) => {
      console.log('Removing Google Ads element with data-google-query-id:', element.id || element.className);
      element.remove();
    });

    // Remove elements with other Google Ads data attributes
    const googleAdElements = this.document.querySelectorAll('[data-google-container-id], [data-google-ad-client]');
    googleAdElements.forEach((element) => {
      console.log('Removing Google Ads element:', element.id || element.className);
      element.remove();
    });

    // Remove Google Ads iframes
    const googleAdsIframes = this.document.querySelectorAll('iframe[id*="google_ads_iframe"]');
    googleAdsIframes.forEach((iframe) => {
      console.log('Removing Google Ads iframe:', iframe.id);
      // Remove the container if it exists
      const container = iframe.closest('[id*="google_ads_iframe"][id*="container"]') || iframe.parentElement;
      if (container && container !== iframe) {
        container.remove();
      } else {
        iframe.remove();
      }
    });

    // Remove any remaining ad containers with Mandiner_ prefix
    const mandinerAdElements = this.document.querySelectorAll('[id*="Mandiner_"]:not(app-strossle-advert)');
    mandinerAdElements.forEach((element) => {
      // Only remove if it's not our Angular component and has ad-related content
      if (!element.hasAttribute('_nghost-') && !element.hasAttribute('_ngcontent-') &&
          (element.querySelector('iframe[id*="google_ads_iframe"]') || element.hasAttribute('data-google-query-id'))) {
        console.log('Removing Mandiner ad container:', element.id);
        element.remove();
      }
    });
  }

  /**
   * Loads advert script conditionally based on user subscription status
   */
  loadAdvertScriptConditionally(user: User | undefined, hasToken: boolean): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    if (!hasToken) {
      this.userAuthenticationChecked = true;
      this.loadAdvertScript();
      return;
    }

    if (user) {
      this.userAuthenticationChecked = true;
      const hasValidSubscriptionAdvertise = (user as any).hasValidSubscriptionAdvertise;

      if (hasValidSubscriptionAdvertise) {
        this.disableAds();
        this.removeAdvertScript();
      } else {
        this.enableAds();
        if (!this.advertScriptLoaded) {
          this.loadAdvertScript();
        }
      }
    }
  }

  /**
   * Handles user login after initial site visit
   */
  handleUserLoginAfterVisit(user: User | undefined): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (!user) {
      return;
    }

    const hasValidSubscriptionAdvertise = (user as any).hasValidSubscriptionAdvertise;

    if (hasValidSubscriptionAdvertise) {
      this.disableAds();
      this.removeAdvertScript();
    } else {
      this.enableAds();
      if (!this.advertScriptLoaded) {
        this.loadAdvertScript();
      }
    }
  }

  /**
   * Checks if user authentication has been processed
   */
  isUserAuthenticationChecked(): boolean {
    return this.userAuthenticationChecked;
  }
}
