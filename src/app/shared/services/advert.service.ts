import { DOCUMENT } from '@angular/common';
import { inject, Inject, Injectable, Injector, runInInjectionContext } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { UtilService } from '@trendency/kesma-core';
import { ScriptLoaderService, User } from '@trendency/kesma-ui';
import { BehaviorSubject, interval, Subject, takeUntil, timer } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AdvertService {
  constructor(
    private readonly injector: Injector,
    private readonly utilsService: UtilService,
    private readonly scriptLoaderService: ScriptLoaderService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  destroyInterval$ = new Subject<void>();
  readonly enableAdsSubject = new BehaviorSubject<boolean>(true);
  private advertScriptLoaded = false;
  private userAuthenticationChecked = false;

  disableAds(): void {
    this.enableAdsSubject.next(false);
    this.addAdsDoNotShowClass();
    this.advertInterval();
    this.removeDynamicAdElements();
    this.preventAdReloading();
  }

  enableAds(): void {
    this.enableAdsSubject.next(true);
    this.destroyInterval$.next();
    this.removeAdsDoNotShowClass();
    this.advertInterval();
  }

  isAdEnabled(): boolean {
    return this.enableAdsSubject.getValue();
  }

  callAdvertScriptOnNavigation(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    setTimeout(() => {
      if (this.isAdEnabled()) {
        this.reloadAds();
      } else {
        this.clearAds();
        // For subscribed users, aggressively prevent ad reloading on navigation
        this.preventAdReloading();
      }
    }, 1000);
  }

  advertInterval(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (this.isAdEnabled()) {
      return;
    }

    const advertCleaner$ = interval(500).pipe(takeUntil(timer(5000)), takeUntil(this.destroyInterval$));
    advertCleaner$.subscribe(() => {
      if (!this.isAdEnabled()) {
        this.clearAds();
      }
    });
  }

  clearAds(): void {
    if (!this.utilsService.isBrowser() || window?.__adsConfig === undefined) return;

    window.__adsConfig?.clearAds();
    this.removeDynamicAdElements();
  }

  reloadAds(): void {
    if (!this.utilsService.isBrowser() || window?.__adsConfig === undefined) return;

    window.__adsConfig?.spaHardReset();
    window.__adsConfig?.reinsertStrossle();
  }

  handleAdDisabling(isWithoutAds: boolean): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (!isWithoutAds === this.enableAdsSubject.getValue()) {
      return;
    }

    if (isWithoutAds) {
      this.disableAds();
    } else {
      this.enableAds();
    }
  }

  loadAdvertScript(): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    if (this.advertScriptLoaded) {
      return;
    }

    setTimeout(() => {
      runInInjectionContext(this.injector, () => {
        const activatedRoute = inject(ActivatedRoute, { optional: true });
        if (activatedRoute?.snapshot?.firstChild?.data?.['skipSsrConditionalElements']) {
          return;
        }
        const todayDate = new Date().toISOString().slice(0, 10).replace(/-/g, '');

        const advertSrc = 'https://cdn-alpha.adsinteractive.com/mandiner.hu.js?v=' + todayDate;

        this.scriptLoaderService.loadScript(advertSrc, true, false);
        this.advertScriptLoaded = true;
      });
    }, 500);
  }

  addAdsDoNotShowClass(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    this.document.body.classList.add('adsDoNotShowAds');
  }

  removeAdsDoNotShowClass(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }
    this.document.body.classList.remove('adsDoNotShowAds');
  }

  /**
   * Removes advert script from the DOM
   */
  removeAdvertScript(): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    const advertScripts = this.document.querySelectorAll('script[src*="adsinteractive.com"]');
    advertScripts.forEach((script) => script.remove());

    if (window?.__adsConfig) {
      window.__adsConfig = undefined;
    }

    this.advertScriptLoaded = false;
    this.removeDynamicAdElements();
  }

  /**
   * Removes dynamically created ad elements that are generated by ad scripts
   */
  private removeDynamicAdElements(): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    // Remove footer banner ads
    const footerBanners = this.document.querySelectorAll('.ads-footer-banner');
    footerBanners.forEach((element) => element.remove());

    // Remove Google Ads iframes and containers
    const googleAdsIframes = this.document.querySelectorAll('iframe[id*="google_ads_iframe"]');
    googleAdsIframes.forEach((iframe) => {
      const container = iframe.closest('[id*="google_ads_iframe"][id*="container"]') || iframe.parentElement;
      if (container) {
        container.remove();
      } else {
        iframe.remove();
      }
    });

    // Remove any elements with Google Ads container IDs
    const googleAdsContainers = this.document.querySelectorAll('[id*="google_ads_iframe"][id*="container"]');
    googleAdsContainers.forEach((container) => container.remove());

    // Remove any divs that contain ad-related classes or IDs
    const adElements = this.document.querySelectorAll('[class*="ads_"], [id*="Mandiner_"]:not(app-strossle-advert)');
    adElements.forEach((element) => {
      // Only remove if it's not our Angular component
      if (!element.hasAttribute('_nghost-') && !element.hasAttribute('_ngcontent-')) {
        element.remove();
      }
    });

    // Remove any remaining ad-related elements
    const adRelatedElements = this.document.querySelectorAll('[data-google-query-id], [data-google-container-id]');
    adRelatedElements.forEach((element) => element.remove());
  }

  /**
   * Loads advert script conditionally based on user subscription status
   */
  loadAdvertScriptConditionally(user: User | undefined, hasToken: boolean): void {
    if (!this.utilsService.isBrowser() || !this.document) {
      return;
    }

    if (!hasToken) {
      this.userAuthenticationChecked = true;
      this.loadAdvertScript();
      return;
    }

    if (user) {
      this.userAuthenticationChecked = true;
      const hasValidSubscriptionAdvertise = (user as any).hasValidSubscriptionAdvertise;

      if (hasValidSubscriptionAdvertise) {
        this.disableAds();
        this.removeAdvertScript();
      } else {
        this.enableAds();
        if (!this.advertScriptLoaded) {
          this.loadAdvertScript();
        }
      }
    }
  }

  /**
   * Handles user login after initial site visit
   */
  handleUserLoginAfterVisit(user: User | undefined): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    if (!user) {
      return;
    }

    const hasValidSubscriptionAdvertise = (user as any).hasValidSubscriptionAdvertise;

    if (hasValidSubscriptionAdvertise) {
      this.disableAds();
      this.removeAdvertScript();
      this.interceptAdScriptLoading();
      this.startAdBlockingObserver();
      this.startPeriodicAdCleanup();
    } else {
      this.enableAds();
      if (!this.advertScriptLoaded) {
        this.loadAdvertScript();
      }
    }
  }

  /**
   * Prevents ad reloading by blocking ad scripts and clearing ad configurations
   */
  private preventAdReloading(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    // Block window.__adsConfig from being recreated
    Object.defineProperty(window, '__adsConfig', {
      value: undefined,
      writable: false,
      configurable: false
    });

    // Block common ad script globals
    const adGlobals = ['googletag', 'adsbygoogle', '__tcfapi'];
    adGlobals.forEach((globalName) => {
      if ((window as any)[globalName]) {
        try {
          Object.defineProperty(window, globalName, {
            value: undefined,
            writable: false,
            configurable: false
          });
        } catch (e) {
          // Some globals might be non-configurable, ignore errors
        }
      }
    });

    // Set flag to prevent ad serving
    window.adsDoNotServeAds = true;

    // Remove any cached ad scripts
    this.removeCachedAdScripts();
  }

  /**
   * Removes cached ad scripts from DOM and prevents their execution
   */
  private removeCachedAdScripts(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    // Remove all ad-related scripts
    const adScripts = this.document.querySelectorAll(`
      script[src*="adsinteractive.com"],
      script[src*="googlesyndication.com"],
      script[src*="googletagservices.com"],
      script[src*="doubleclick.net"]
    `);
    adScripts.forEach((script) => script.remove());

    // Remove inline scripts that might reload ads
    const inlineScripts = this.document.querySelectorAll('script:not([src])');
    inlineScripts.forEach((script) => {
      const content = script.textContent || script.innerHTML;
      if (content.includes('__adsConfig') ||
          content.includes('googletag') ||
          content.includes('adsinteractive')) {
        script.remove();
      }
    });
  }

  /**
   * Starts periodic cleanup of dynamic ad elements for subscribed users
   */
  private startPeriodicAdCleanup(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    const two_seconds = 2000;
    const ten_seconds = 10000;
    const cleanup$ = interval(two_seconds).pipe(takeUntil(timer(ten_seconds)), takeUntil(this.destroyInterval$));
    cleanup$.subscribe(() => {
      if (!this.isAdEnabled()) {
        this.removeDynamicAdElements();
        this.preventAdReloading();
      }
    });
  }

  /**
   * Starts a MutationObserver to block ad elements as they're added to the DOM
   */
  private startAdBlockingObserver(): void {
    if (!this.utilsService.isBrowser() || !window.MutationObserver) {
      return;
    }

    const observer = new MutationObserver((mutations) => {
      if (!this.isAdEnabled()) {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;

              // Block ad-related elements
              if (element.classList?.contains('ads-footer-banner') ||
                  element.id?.includes('google_ads_iframe') ||
                  element.classList?.toString().includes('ads_') ||
                  element.tagName === 'SCRIPT' && (element as HTMLScriptElement).src?.includes('adsinteractive.com')) {
                console.log('Blocked ad element from being added:', element);
                element.remove();
              }

              // Check child elements for ads
              const adElements = element.querySelectorAll?.('.ads-footer-banner, [id*="google_ads_iframe"], [class*="ads_"]');
              adElements?.forEach((adElement) => {
                console.log('Blocked nested ad element:', adElement);
                adElement.remove();
              });
            }
          });
        });
      }
    });

    observer.observe(this.document.body, {
      childList: true,
      subtree: true
    });

    // Store observer reference to disconnect later if needed
    (this as any).adBlockingObserver = observer;
  }

  /**
   * Intercepts and blocks ad script loading at the network level
   */
  private interceptAdScriptLoading(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    // Override createElement to block ad script creation
    const originalCreateElement = this.document.createElement.bind(this.document);
    this.document.createElement = function(tagName: string): HTMLElement {
      const element = originalCreateElement(tagName);

      if (tagName.toLowerCase() === 'script') {
        const originalSetAttribute = element.setAttribute.bind(element);
        element.setAttribute = function(name: string, value: string): void {
          if (name === 'src' && (
            value.includes('adsinteractive.com') ||
            value.includes('googlesyndication.com') ||
            value.includes('googletagservices.com') ||
            value.includes('doubleclick.net')
          )) {
            console.log('Blocked ad script loading:', value);
            return; // Block the script loading
          }
          return originalSetAttribute(name, value);
        };
      }

      return element;
    };

    // Override appendChild to block ad script injection
    const originalAppendChild = this.document.head.appendChild.bind(this.document.head);
    this.document.head.appendChild = function<T extends Node>(child: T): T {
      if (child.nodeName === 'SCRIPT' && (child as any).src && (
        (child as any).src.includes('adsinteractive.com') ||
        (child as any).src.includes('googlesyndication.com') ||
        (child as any).src.includes('googletagservices.com') ||
        (child as any).src.includes('doubleclick.net')
      )) {
        console.log('Blocked ad script injection:', (child as any).src);
        return child; // Return the element but don't append it
      }
      return originalAppendChild(child);
    };
  }

  /**
   * Checks if user authentication has been processed
   */
  isUserAuthenticationChecked(): boolean {
    return this.userAuthenticationChecked;
  }
}
