import { Injectable } from '@angular/core';
import { buildPhpArrayParam, IHttpOptions, ReqService } from '@trendency/kesma-core';
import {
  ApiListResult,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleCategoryParams,
  ArticleSearchResult,
  BackendArticleSearchResult,
  BackendComment,
  BasicDossier,
  buildMenuItem,
  ColumnTreeElement,
  CommentListResponse,
  CommentRequestParams,
  DossierArticle,
  GalleriesResponse,
  InitResponse,
  Layout,
  mapToCommentListResponse,
  MenuChild,
  MenuTreeResponse,
  OlimpiaHungarianCompetitions,
  OlimpiaMedalsNational,
  Ordering,
  PortalConfigSetting,
  PortfolioResponse,
  PrimaryColumn,
  SearchQuery,
  SimpleComment,
  SimplifiedMenuItem,
  SimplifiedMenuTree,
  ThumbnailImage,
  TrendingTag,
} from '@trendency/kesma-ui';
import { MedalTable } from '@trendency/kesma-ui/lib/components/olimpia/olimpia-medal-table/olimpia-medal-table.definitions';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { requestPasswordResetDataToBackendRequest, resetPasswordDataToBackendRequest } from '../../feature/forgot-password/forgot-password.utils';
import { backendArticlesSearchResultsToArticleSearchResArticles } from '../../feature/tags-page/tags-page.utils';
import {
  ArticleCardWithSecondaryColumns,
  AuthorData,
  BackendAllowedLoginMethodsResponse,
  BackendAuthorData,
  BackendSocialLoginResponse,
  BackendUserLoginResponse,
  BackendUserResponse,
  Calendar,
  DossierCard,
  HeaderMagazineData,
  IAthorSearchListParams,
  ISearchParams,
  JournalData,
  JournalDataWithArticles,
  LoginFormData,
  RegistrationFormData,
  VerifyRegisterResponse,
} from '../definitions';
import { loginFormDataToBackendRequest, registrationFormDataToBackendRequest } from '../utils';
import { PortalConfigService } from './portal-config.service';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  constructor(
    private readonly reqService: ReqService,
    private readonly portalConfigService: PortalConfigService
  ) {}

  init(): Observable<ApiResult<InitResponse>> {
    return this.reqService.get('/init');
  }

  getMenu(): Observable<SimplifiedMenuTree> {
    return this.reqService.get<ApiResult<MenuTreeResponse>>('menu/tree').pipe(
      map(
        ({ data, meta }) =>
          ({
            header: (data?.header ?? []).map((item) => this.mapBackendHeaderToHeader(item)),
            header_0: (data?.header_0 ?? []).map((item) => buildMenuItem(item)),
            header_1: (data?.header_1 ?? []).map((item) => buildMenuItem(item)),
            footer: (data?.footer ?? []).map((item) => buildMenuItem(item)),
            footer_0: (data?.footer_0 ?? []).map((item) => buildMenuItem(item)),
            footer_1: (data?.footer_1 ?? []).map((item) => buildMenuItem(item)),
            moreHeaderConfigNewsletter: meta?.['moreHeaderConfigNewsletter'] || false,
            moreHeaderConfigSubscription: meta?.['moreHeaderConfigSubscription'] || false,
          }) as SimplifiedMenuTree
      )
    );
  }

  mapBackendHeaderToHeader(item: MenuChild): SimplifiedMenuItem {
    return {
      ...buildMenuItem(item),
      relatedType: item.relatedType,
    } as SimplifiedMenuItem;
  }

  getLayoutPreview(hash: string): Observable<ApiResult<Layout>> {
    return this.reqService.get(`layout/preview/view?previewHash=${hash}`);
  }

  getCategoryLayout(categorySlug: string): Observable<ApiResult<Layout>> {
    return this.reqService.get<ApiResult<Layout>>(`column-layout/${categorySlug}`);
  }

  public getSidebarArticleRecommendations(
    count: number,
    columnSlug?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: count.toString(),
        'excludedArticleIds[]': excludedIds,
      },
    };
    params = columnSlug ? { ...params, columnSlug } : params;

    return this.reqService.get<ApiResult<ArticleCard[], ApiResponseMetaList>>(
      columnSlug ? '/content-page/articles-by-column?dev' : '/content-page/articles-by-last-day',
      { params }
    );
  }

  public getArticleViewAnalytics(articleId: string, uuid: string): Record<string, any> {
    return this.reqService.get(`/article-view/${articleId}`, {
      params: {
        a: uuid,
      },
    });
  }

  getAuthorFromPublicAuthor(authorSlug: string): Observable<ApiResult<AuthorData>> {
    return this.reqService.get(`/user/author_social`, {
      params: {
        global_filter: authorSlug,
      },
    });
  }

  public getCategoryArticles(
    categorySlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = [],
    orderByAsc?: boolean,
    searchQuery?: string,
    fromDate?: string,
    toDate?: string,
    contentTypes: string[] = [],
    columns: string[] = []
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let params: ArticleCategoryParams = {
      columnSlug: categorySlug,
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      'excludedArticleIds[]': excludedIds,
      'content_types[]': contentTypes,
      'columnSlugs[]': columns,
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;
    params = fromDate ? { ...params, fromDate } : params;
    params = toDate ? { ...params, toDate } : params;

    Object.assign(params, { 'publishDate_order[0]': orderByAsc ? 'asc' : 'desc' });

    if (searchQuery) {
      Object.assign(params, { global_filter: searchQuery });
    }

    return this.reqService.get<ApiResult<ArticleCard[], ApiResponseMetaList>>(`content-page/articles-by-column`, { params }).pipe(
      map(({ data, meta }) => ({
        meta,
        data: data.map((article: ArticleCard) => {
          const [publishYear, publishMonth] = (article.publishDate as string).split('-');
          return {
            ...article,
            publishYear,
            publishMonth,
          };
        }),
      }))
    );
  }

  getOpinionAuthor(
    authorSlug: string,
    page = 0,
    itemsPerPage = 12,
    orderByAsc?: boolean,
    searchQuery?: string,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    columns?: string[]
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    let params: IAthorSearchListParams = {
      author: authorSlug,
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      global_filter: searchQuery || '',
      'publishDate_order[0]': orderByAsc ? 'asc' : 'desc',
      'content_types[]': contentTypes ? contentTypes : [],
      'columnSlugs[]': columns ? columns : [],
    };
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;

    return this.reqService.get(`content-page/articles-by-opinion-type`, {
      params,
    });
  }

  getArticlesByOwnMaterialType(
    authorSlug: string,
    page = 0,
    itemsPerPage = 12,
    orderByAsc?: boolean,
    searchQuery?: string,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    columns?: string[]
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    const enabledExternalSlug = this.portalConfigService.isConfigSet(PortalConfigSetting.ENABLE_EXTERNAL_PUBLIC_AUTHOR_SLUG);
    let params: IAthorSearchListParams = {
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      global_filter: searchQuery || '',
      'publishDate_order[0]': orderByAsc ? 'asc' : 'desc',
      'content_types[]': contentTypes ? contentTypes : [],
      'columnSlugs[]': columns ? columns : [],
    };
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;

    if (enabledExternalSlug) {
      params.authorSlug = authorSlug;
    } else {
      params.author = authorSlug;
    }

    return this.reqService.get(`content-page/articles-by-own-material-type`, {
      params,
    });
  }

  getArticlesByTag(slug: string): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-tag?tagSlug=${slug}`);
  }

  getGalleries(page = 0, itemsPerPage = 21): Observable<ApiResult<GalleriesResponse[], ApiResponseMetaList>> {
    return this.reqService.get(`/media/galleries`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  getDossier(dossierSlug: string, page = 0, itemsPerPage = 21): Observable<ApiResult<DossierArticle[], ApiResponseMetaList & Partial<BasicDossier>>> {
    return this.reqService.get(`/content-group/dossiers/${dossierSlug}`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  getDossiers(page = 0, itemsPerPage = 21): Observable<ApiResult<DossierCard[], ApiResponseMetaList>> {
    return this.reqService.get(`/content-group/dossiers`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  searchByKeyword(
    global_filter?: string,
    sort?: string,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    column?: string[],
    page = 0,
    rowCount_limit = 20,
    material_types_only?: string,
    tag?: string[],
    priority?: string[]
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    let params: ISearchParams = {
      rowCount_limit: rowCount_limit?.toString(),
      page_limit: page?.toString(),
      'publishDate_order[0]': sort === 'date_asc' ? 'asc' : 'desc',
      'content_types[]': contentTypes ? contentTypes : [],
      'columnSlugs[]': column ? column : [],
      'tagSlugs[]': tag ? tag : [],
      'priorityIds[]': priority ? priority : [],
    };
    params = global_filter ? { ...params, global_filter } : params;
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;
    params = material_types_only ? { ...params, material_types_only } : params;

    return this.reqService.get(`/content-page/search`, {
      params,
    });
  }

  /**
   * Returns the list of all authors
   * @param page - page number, starts from 0
   * @param perPage - number of items per page, default 10
   * @param isInner - is inner author
   */
  getAuthors(page: number, perPage = 10, isInner?: boolean): Observable<ApiResult<BackendAuthorData[], ApiResponseMetaList>> {
    const params = {
      rowCount_limit: perPage.toString(),
      page_limit: page.toString(),
    };

    if (isInner) {
      Object.assign(params, { is_inner: '1' });
    }

    return this.reqService.get('user/authors', {
      params,
    });
  }

  getVideosAndPodcasts(
    contentTypes: string[],
    searchQuery: string,
    page?: number,
    rowCountLimit?: number,
    orderByAsc?: boolean
  ): Observable<ApiResult<BackendArticleSearchResult[]>> {
    return this.reqService.get('/content-page/search', {
      params: {
        global_filter: searchQuery,
        rowCount_limit: rowCountLimit?.toString(),
        page_limit: page?.toString(),
        'content_types[]': contentTypes,
        'publishDate_order[0]': orderByAsc ? 'asc' : 'desc',
      },
    });
  }

  getVideoTypeArticles(
    searchQuery: string,
    page?: number,
    rowCountLimit?: number,
    orderByAsc?: string,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    column?: string[]
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let params: ISearchParams = {
      global_filter: searchQuery,
      rowCount_limit: rowCountLimit?.toString(),
      page_limit: page?.toString(),
      'publishDate_order[0]': orderByAsc === 'date_asc' ? 'asc' : 'desc',
      'content_types[]': contentTypes ? contentTypes : [],
      'columnSlugs[]': column ? column : [],
    };
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;

    return this.reqService.get(`/content-page/articles-by-video-type`, {
      params,
    });
  }

  getNewsArticles(
    page?: number,
    rowCountLimit?: number,
    orderByAsc?: boolean,
    from_date?: string,
    to_date?: string,
    contentTypes?: string[],
    column?: string[]
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let params: ISearchParams = {
      rowCount_limit: rowCountLimit?.toString(),
      page_limit: page?.toString(),
      'publishDate_order[0]': orderByAsc ? 'asc' : 'desc',
      'content_types[]': contentTypes ? contentTypes : [],
      'columnSlugs[]': column ? column : [],
    };
    params = from_date ? { ...params, from_date } : params;
    params = to_date ? { ...params, to_date } : params;

    return this.reqService.get<ApiResult<ArticleCard[], ApiResponseMetaList>>(`/content-page/articles-by-any`, { params }).pipe(
      map((apiResult) => ({
        meta: apiResult.meta,
        data: apiResult.data.map((article: ArticleCard) => ({
          ...article,
          thumbnail: {
            url: (article.thumbnail as unknown as string) ?? '',
            // BE returns thumbnail as string instead of object here, unlike in other places
          } as ThumbnailImage,
          hasGallery: article.hasGallery ? !!+article.hasGallery : false,
          isPaywalled: article.isPaywalled ? !!+article.isPaywalled : false,
        })),
      }))
    );
  }

  getPortfolioFooter(): Observable<PortfolioResponse> {
    return this.reqService.get('portal/portfolio-footer');
  }

  public getFreshArticles(columnSlugs?: string[], tagSlugs?: string[], priorityId?: string[]): Observable<ArticleCard[]> {
    return this.reqService
      .get<ApiResult<ArticleCard[]>>('/content-page/articles-by-last-day', {
        params: {
          'columnSlugs[]': columnSlugs || [],
          'tagSlugs[]': tagSlugs || [],
          'priorityIds[]': priorityId || [],
          'content_types[]': 'article',
        },
      })
      .pipe(map((apiResult: ApiResult<ArticleCard[]>) => apiResult.data));
  }

  getJournals(from = 0, count = 12): Observable<ApiResult<JournalDataWithArticles[], ApiResponseMetaList>> {
    return this.reqService.get('content-group/journal-issue/list', {
      params: {
        rowCount_limit: count,
        rowFrom_limit: from,
      },
    });
  }

  getJournal(journalSlug: string): Observable<ApiResult<JournalData, ApiResponseMetaList>> {
    return this.reqService.get(`content-group/journal-issue/${journalSlug}/slug/show`);
  }

  getArticlesByJournal(journalSlug: string): Observable<ApiResult<ArticleCardWithSecondaryColumns[], ApiResponseMetaList>> {
    const params: Record<string, string | number> = {
      journalIssue: journalSlug,
      rowCount_limit: 100,
    };

    return this.reqService
      .get<ApiResult<ArticleCardWithSecondaryColumns[], ApiResponseMetaList>>('content-page/articles-by-journal-issue-slug', { params })
      .pipe(
        map((apiResult) => ({
          meta: apiResult.meta,
          data: apiResult.data.map((article: ArticleCardWithSecondaryColumns) => ({
            ...article,
            thumbnail: {
              url: (article.thumbnail as unknown as string) ?? '',
              // BE returns thumbnail as string instead of object here, unlike in other places
            } as ThumbnailImage,
          })),
        }))
      );
  }

  getHeaderMagazine(): Observable<ApiResult<HeaderMagazineData> | null> {
    // To avoid 403 calls, we check if the portal config is set so the endpoint is available
    if (!this.portalConfigService.isConfigSet(PortalConfigSetting.MENU_MORE_HEADER_CONFIG_CHECKBOX_JOURNAL_ISSUE)) {
      return of(null);
    }

    return this.reqService.get<ApiResult<HeaderMagazineData>>('content-group/journal-issue/view-current').pipe(catchError(() => of(null)));
  }

  getParentColumns(): Observable<ApiResult<PrimaryColumn[], ApiResponseMetaList>> {
    return this.reqService.get('/source/content-group/columns?parents_only=1&rowCount_limit=9999');
  }

  getTagsOnHeaderBar(): Observable<ApiResult<TrendingTag[], ApiResponseMetaList>> {
    return this.reqService.get('/content-group/tags-on-header-bar');
  }

  /**
   * Returns the list of all comments for a given article.
   * @param articleId The article id
   * @param sort The sorting type. Valid values are 'most-popular', 'latest', 'oldest'. Default is 'latest'.
   * @param params Additional params for the request
   */
  getCommentListForArticle(articleId: string, sort: Ordering = 'latest', params: Partial<CommentRequestParams> = {}): Observable<CommentListResponse> {
    const sorting = {
      'most-popular': { 'likeCount_order[0]': 'desc', 'createdAt_order[1]': 'desc' },
      latest: { 'createdAt_order[0]': 'desc' },
      oldest: { 'createdAt_order[0]': 'asc' },
    }[sort];
    return this.reqService
      .get<ApiResult<BackendComment[], ApiResponseMetaList>>(`comments/article/${articleId}/comments`, {
        params: { ...params, ...sorting },
      })
      .pipe(mapToCommentListResponse());
  }

  /**
   * Returns the list of all answers for a given comment.
   * @param commentId The comment id
   * @param sort
   * @param params The limit params for the pagination
   */
  getAnswersForComment(
    commentId: string,
    sort: Ordering = 'latest',
    params?: { page_limit?: number; rowCount_limit?: number }
  ): Observable<CommentListResponse> {
    const sorting = {
      'most-popular': { 'likeCount_order[0]': 'desc', 'createdAt_order[1]': 'desc' },
      latest: { 'createdAt_order[0]': 'desc' },
      oldest: { 'createdAt_order[0]': 'asc' },
    }[sort];
    return this.reqService
      .get<ApiResult<BackendComment[], ApiResponseMetaList>>(`comments/comment/${commentId}/answers`, { params: { ...params, ...sorting } })
      .pipe(mapToCommentListResponse());
  }

  register(formData: RegistrationFormData, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`user/register`, registrationFormDataToBackendRequest(formData, recaptchaToken));
  }

  verifyRegister(id: string, hashedEmail: string, expiration: string, signature: string): Observable<VerifyRegisterResponse> {
    return this.reqService.get(`user/register/verify/${id}/${hashedEmail}`, { params: { expiration, signature } });
  }

  resendVerificationEmail(email: string): Observable<void> {
    return this.reqService.post(`user/register/verify-email-resend`, { email });
  }

  savePassword(savePasswordUrl: string, userName: string, password: string): Observable<void> {
    return this.reqService.post(savePasswordUrl, { userName, password });
  }

  login(formData: LoginFormData, recaptchaToken: string): Observable<BackendUserLoginResponse> {
    return this.reqService.post(`portal_user/auth/login_check`, loginFormDataToBackendRequest(formData, recaptchaToken));
  }

  requestPasswordReset(email: string, recaptchaToken: string): Observable<void> {
    return this.reqService.post(`user/password/forget`, requestPasswordResetDataToBackendRequest(email, recaptchaToken));
  }

  resetPassword(email: string, password: string, resetPasswordToken: string): Observable<void> {
    return this.reqService.post(`user/password/reset`, resetPasswordDataToBackendRequest(email, password, resetPasswordToken));
  }

  getAllowedLoginMethods(): Observable<BackendAllowedLoginMethodsResponse> {
    return this.reqService.get(`user/auth/allowed-logins`);
  }

  loginWithFacebook(code: string, redirectUri: string): Observable<BackendSocialLoginResponse> {
    return this.reqService.post(`user/auth/login-facebook`, { code, redirectUri });
  }

  loginWithGoogle(code: string, redirectUri: string): Observable<BackendSocialLoginResponse> {
    return this.reqService.post(`user/auth/login-google`, { code, redirectUri });
  }

  loginWithApple(code: string): Observable<BackendSocialLoginResponse> {
    return this.reqService.post(`user/auth/login-apple`, { code });
  }

  getCalendar(): Observable<ApiResult<Calendar>> {
    return this.reqService.get<ApiResult<Calendar>>('content-page/calendar');
  }

  getCommentWars(days = 1): Observable<BackendArticleSearchResult[]> {
    const fromDate = new Date(Date.now() - days * 86_400_000); // millis in a day
    return this.reqService.get<BackendArticleSearchResult[]>(`content-page/articles/comment-wars`, {
      params: {
        fromDate: `${fromDate.getFullYear()}-${fromDate.getMonth() + 1}-${fromDate.getDate()}`,
      },
    });
  }

  getPublicUser(id: string): Observable<BackendUserResponse> {
    console.log('user data loaded');
    return this.reqService.get(`portal-user/${id}`);
  }

  getCommentHistory(commentId: string): Observable<ApiListResult<SimpleComment>> {
    return this.reqService.get<ApiListResult<SimpleComment>>(`comments/comment/${commentId}/history`);
  }

  getColumnsAsTree(): Observable<ApiResult<ColumnTreeElement[], ApiResponseMetaList>> {
    return this.reqService.get(`source/content-group/columns-as-tree`);
  }

  getSearch(searchQuery: SearchQuery, page = 0, itemsPerPage = 20): Observable<ApiListResult<ArticleSearchResult>> {
    return this.reqService.get(`content-page/search?`, {
      params: {
        ...searchQuery,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  getOlimpiaCompetitions(startDate: string, countryCode = 'hun'): Observable<ApiResult<OlimpiaHungarianCompetitions[], ApiResponseMetaList>> {
    return this.reqService.get(`/olympics/events/2024?countryCode_filter=${countryCode}&startDate_filter=${startDate}`);
  }

  getOlimpiaCountryMedals(year = '2024'): Observable<ApiResult<MedalTable[]>> {
    return this.reqService.get(`/olympics/country-medallist/${year}`);
  }

  getOlimpiaNationalMedals(year = '2024', countryCode = 'hun'): Observable<ApiResult<OlimpiaMedalsNational[]>> {
    return this.reqService.get(`/olympics/participant-medallist/${year}?countryCode_filter=${countryCode}`);
  }

  getArticlesByFoundationTag(foundationTag: string): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get(`content-page/search`, {
        params: {
          foundationTagSelect: foundationTag,
          rowCount_limit: '1',
          page_limit: '0',
          isFoundationContent: '1',
        },
      })
      .pipe(
        map(({ data, meta }: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList> | any) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }

  searchArticleByTags(tags: string[], contentTypes?: string[], page = 0, itemsPerPage = 10): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get(`content-page/search`, {
        params: {
          ...buildPhpArrayParam(tags, 'tags'),
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
          'content_types[]': contentTypes ? contentTypes : [],
        },
      })
      .pipe(
        map(({ data, meta }: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList> | any) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }
}
