import { Injectable } from '@angular/core';
import { StorageService, UtilService } from '@trendency/kesma-core';
import { EnvironmentApiUrl } from '@trendency/kesma-core/lib/definitions/environment.definitions';
import { User } from '@trendency/kesma-ui';
import { BehaviorSubject, combineLatest, Observable, of, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { ApiService } from './api.service';
import { SecureApiService } from './secure-api.service';
import { BackendSocialLoginResponse, BackendUserLoginResponse, BackendUserResponse, LoginFormData, SocialProvider, UserExtraData } from '../definitions';
import { SUBSCRIPTION_SESSION_KEY } from '../constants';

const TOKEN_STORAGE_KEY = 'token';

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  readonly currentUserSubject: BehaviorSubject<User | undefined> = new BehaviorSubject<User | undefined>(undefined);
  readonly isRememberMeSubject: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  readonly currentUserExtraDataSubject: BehaviorSubject<UserExtraData | undefined> = new BehaviorSubject<UserExtraData | undefined>(undefined);

  readonly SOCIAL_REDIRECT_URL: string = encodeURI(environment.siteUrl + '/regisztracio/veglegesites');

  constructor(
    private readonly apiService: ApiService,
    private readonly secureApiService: SecureApiService,
    private readonly storageService: StorageService,
    private readonly utilsService: UtilService
  ) {}

  get currentUser(): User | undefined {
    return this.currentUserSubject.value;
  }

  get rememberMe(): boolean {
    return this.isRememberMeSubject.value;
  }

  get currentUserExtraData(): UserExtraData | undefined {
    return this.currentUserExtraDataSubject.value;
  }

  authenticate(formData: LoginFormData, recaptchaToken: string): Observable<boolean> {
    return this.apiService.login(formData, recaptchaToken).pipe(
      switchMap((response: BackendUserLoginResponse) => {
        this.setToken(response.token);
        return this.isAuthenticated();
      }),
      catchError((error) => {
        this.clearTokens();
        return throwError(error);
      })
    );
  }

  invalidate(): Observable<boolean> {
    return this.secureApiService.logout().pipe(
      map(() => {
        this.clearTokens();
        return true;
      }),
      catchError(() => {
        this.clearTokens();
        return of(true);
      })
    );
  }

  isAuthenticated(): Observable<boolean> {
    if (this.currentUser) {
      return of(true);
    } else {
      const token = this.getToken();
      if (token) {
        return combineLatest([
          this.secureApiService.getCurrentUser(),
          this.secureApiService.getFollowedAuthors(),
          this.secureApiService.getFollowedCategories(),
        ]).pipe(
          map(([user, followedAuthors, followedCategories]) => {
            this.currentUserSubject.next(this.mapBackendUserResponseToUser(user));
            this.currentUserExtraDataSubject.next({
              followedAuthors: <AUTHORS>
              followedColumns: followedCategories.data.map((c) => c.slug) ?? [],
            });
            return true;
          }),
          catchError(() => {
            this.clearTokens();
            return of(false);
          })
        );
      } else {
        this.clearTokens();
        return of(false);
      }
    }
  }

  getToken(): string | undefined {
    if (!this.utilsService.isBrowser()) {
      return undefined;
    }

    return this.storageService.getLocalStorageData(TOKEN_STORAGE_KEY) as string | undefined;
  }

  setToken(token: string): void {
    // Always store token in localStorage to persist across tabs
    this.storageService.setLocalStorageData(TOKEN_STORAGE_KEY, token);
  }

  clearTokens(): void {
    this.storageService.removeLocalStorageData(TOKEN_STORAGE_KEY);
    this.storageService.removeSessionStorageData(SUBSCRIPTION_SESSION_KEY);
    this.currentUserSubject.next(undefined);
    this.currentUserExtraDataSubject.next(undefined);
  }

  getSocialProviderAuthUrl(provider: SocialProvider): string {
    let providerUrl = '';
    let clientId = '';
    let redirectUrl = this.SOCIAL_REDIRECT_URL;
    let extraParams = '';

    switch (provider) {
      case SocialProvider.FACEBOOK:
        providerUrl = `https://www.facebook.com/v16.0/dialog/oauth`;
        clientId = environment.facebookAppId ?? '';
        break;
      case SocialProvider.GOOGLE:
        providerUrl = `https://accounts.google.com/o/oauth2/v2/auth`;
        clientId = environment.googleClientId ?? '';
        break;
      case SocialProvider.APPLE:
        providerUrl = 'https://appleid.apple.com/auth/authorize';
        clientId = environment.appleClientId ?? '';
        redirectUrl = encodeURI(((environment.apiUrl as EnvironmentApiUrl)?.clientApiUrl || environment.apiUrl) + '/user/auth/login-apple-callback/mandiner');
        extraParams = '&response_mode=form_post';
        break;
    }

    return (
      `${providerUrl}` +
      `?client_id=${clientId}` +
      `&redirect_uri=${redirectUrl}` +
      `&response_type=code` +
      `&scope=email` +
      `&state=${provider}` +
      `${extraParams}`
    );
  }

  authenticateWithSocialProvider(provider: SocialProvider, code: string): Observable<boolean> {
    let providerAuthRequest$: Observable<BackendSocialLoginResponse>;

    switch (provider) {
      case SocialProvider.FACEBOOK:
        providerAuthRequest$ = this.apiService.loginWithFacebook(code, this.SOCIAL_REDIRECT_URL);
        break;
      case SocialProvider.GOOGLE:
        providerAuthRequest$ = this.apiService.loginWithGoogle(code, this.SOCIAL_REDIRECT_URL);
        break;
      case SocialProvider.APPLE:
        providerAuthRequest$ = this.apiService.loginWithApple(code);
        break;
    }

    return providerAuthRequest$.pipe(
      switchMap((response: BackendSocialLoginResponse) => {
        this.setToken(response.token);
        return response.registrationFinished ? this.isAuthenticated() : of(false);
      }),
      catchError((error) => {
        this.clearTokens();
        return throwError(error);
      })
    );
  }

  mapBackendUserResponseToUser(backendUser: BackendUserResponse): User & { isOldOtpSimplePayPayment?: boolean } {
    return {
      uid: backendUser.id,
      email: backendUser.email,
      lastName: backendUser.lastName,
      firstName: backendUser.firstName,
      username: backendUser.userName,
      marketingLetter: backendUser.marketingLetter,
      newsletter: backendUser.newsletter,
      acceptTerms: backendUser.acceptTerms,
      passwordLastSave: backendUser.passwordLastSave,
      hasValidSubscription: backendUser.hasValidSubscription,
      subscription: backendUser.subscription,
      userDetails: backendUser.userDetails,
      isOldOtpSimplePayPayment: backendUser.isOldOtpSimplePayPayment,
    };
  }
}
